use anchor_lang::prelude::*;
use crate::{
    constants::{RouteConfig, FlashLoanConfig},
    state::{RouterConfig, UserPosition},
    error::RouteError,
};

/// 全局安全检查
pub fn validate_global_security_checks(
    config: &RouterConfig,
    user_position: &UserPosition,
    route_config: &RouteConfig,
) -> Result<()> {
    // 检查全局紧急停止
    require!(
        !config.emergency_stop,
        RouteError::GlobalEmergencyStop
    );

    // 检查用户是否被暂停
    require!(
        !user_position.is_suspended,
        RouteError::UserSuspended
    );

    // 检查用户风险评级
    require!(
        user_position.risk_score <= config.max_risk_score,
        RouteError::RiskTooHigh
    );

    // 检查路由参数
    config.validate_route_params(
        route_config.amount_in,
        route_config.routes.len(),
        route_config.max_slippage_bps,
    )?;

    // 检查所有DEX是否被支持且未紧急停止
    for route in &route_config.routes {
        let dex_supported = config.supported_dexes.contains(&route.dex);
        require!(
            dex_supported,
            RouteError::UnsupportedDex
        );

        let dex_emergency_stopped = config.dex_emergency_stops.contains(&route.dex);
        require!(
            !dex_emergency_stopped,
            RouteError::DexEmergencyStop
        );
    }

    Ok(())
}

/// 套利安全检查
pub fn validate_arbitrage_security_checks(
    config: &RouterConfig,
    user_position: &UserPosition,
    arbitrage_config: &RouteConfig,
    flash_loan_config: &FlashLoanConfig,
) -> Result<()> {
    // 先执行全局安全检查
    validate_global_security_checks(config, user_position, arbitrage_config)?;

    // 检查闪电贷功能是否启用
    require!(
        config.flash_loan_enabled,
        RouteError::FlashLoanDisabled
    );

    // 检查用户闪电贷权限
    require!(
        user_position.flash_loan_approved,
        RouteError::FlashLoanNotApproved
    );

    // 检查闪电贷提供者是否在白名单中
    require!(
        config.approved_flash_loan_providers.contains(&flash_loan_config.provider_program),
        RouteError::UnauthorizedFlashLoanProvider
    );

    // 检查闪电贷金额限制
    require!(
        flash_loan_config.amount <= config.max_flash_loan_amount,
        RouteError::FlashLoanAmountExceededNew
    );

    // 检查用户日闪电贷限额
    let daily_volume = user_position.get_daily_volume()?;
    require!(
        daily_volume + flash_loan_config.amount <= config.max_daily_flash_loan_per_user,
        RouteError::DailyLimitExceeded
    );

    Ok(())
}

/// 验证路由连续性
pub fn validate_route_continuity(routes: &[crate::constants::Route]) -> Result<()> {
    require!(
        !routes.is_empty(),
        RouteError::EmptyRoute
    );

    for i in 1..routes.len() {
        require!(
            routes[i-1].output_mint == routes[i].input_mint,
            RouteError::RouteDiscontinuity
        );
    }

    Ok(())
}

/// 验证循环路由
pub fn validate_circular_route(routes: &[crate::constants::Route]) -> Result<()> {
    validate_route_continuity(routes)?;

    require!(
        routes.len() >= 2,
        RouteError::InvalidRouteSteps
    );

    let start_token = routes.first().unwrap().input_mint;
    let end_token = routes.last().unwrap().output_mint;

    require!(
        start_token == end_token,
        RouteError::NotCircularRoute
    );

    Ok(())
}

/// 验证代币账户所有权
pub fn validate_token_account_ownership(
    account: &anchor_spl::token_interface::TokenAccount,
    expected_owner: &Pubkey,
) -> Result<()> {
    require!(
        account.owner == *expected_owner,
        RouteError::InvalidTokenAccountOwner
    );

    Ok(())
}

/// 验证代币余额充足
pub fn validate_sufficient_balance(
    account: &anchor_spl::token_interface::TokenAccount,
    required_amount: u64,
) -> Result<()> {
    require!(
        account.amount >= required_amount,
        RouteError::InsufficientBalance
    );

    Ok(())
}

/// 验证金额合理性
pub fn validate_amounts(
    amount_in: u64,
    min_amount_out: u64,
    max_slippage_bps: u16,
) -> Result<()> {
    require!(
        amount_in > 0,
        RouteError::ZeroAmount
    );

    require!(
        min_amount_out > 0,
        RouteError::ZeroAmount
    );

    require!(
        max_slippage_bps <= 1000, // 最大10%
        RouteError::InvalidSlippage
    );

    Ok(())
}

/// 验证管理员权限
pub fn validate_admin_authority(
    admin: &Signer,
    config: &RouterConfig,
) -> Result<()> {
    require!(
        admin.is_signer,
        RouteError::UnauthorizedAdmin
    );

    require!(
        admin.key() == config.admin,
        RouteError::UnauthorizedAdmin
    );

    Ok(())
}

/// 计算价格影响
pub fn calculate_price_impact(
    amount_in: u64,
    amount_out: u64,
    expected_rate: f64,
) -> Result<u16> {
    if amount_in == 0 {
        return Ok(0);
    }

    let actual_rate = (amount_out as f64) / (amount_in as f64);
    let price_impact = ((expected_rate - actual_rate) / expected_rate * 10000.0) as u16;

    Ok(price_impact)
}

/// 验证时间戳有效性
pub fn validate_timestamp(timestamp: i64, max_age_seconds: i64) -> Result<()> {
    let current_time = Clock::get()?.unix_timestamp;

    require!(
        timestamp > current_time - max_age_seconds,
        RouteError::DataTooOld
    );

    require!(
        timestamp <= current_time,
        RouteError::FutureTimestamp
    );

    Ok(())
}

/// 验证PDA派生
pub fn validate_pda_derivation(
    pda: &Pubkey,
    seeds: &[&[u8]],
    program_id: &Pubkey,
) -> Result<u8> {
    let (expected_pda, bump) = Pubkey::find_program_address(seeds, program_id);

    require!(
        *pda == expected_pda,
        RouteError::InvalidPDA
    );

    Ok(bump)
}